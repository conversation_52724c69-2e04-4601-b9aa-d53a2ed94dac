using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace OP.BlockSand
{
    /// <summary>
    /// Configuration for shape spawning rules based on level
    /// </summary>
    [System.Serializable]
    public class LevelSpawnConfig
    {
        [Header("Basic Settings")]
        public int level;

        public int colorCount = 4; // Number of different colors/materials to use
        public List<BlockType> availableBlockTypes = new List<BlockType>(); // Available block types for this level

        [Header("Multi-color Shape Settings")]
        [Range(0f, 1f)]
        public float multiColorShapeChance = 0f; // Chance to spawn a shape with 2 colors

        [Header("Clear Guarantee Settings")]
        public int spawnAllCountForGuaranteedClear = 4; // After this many spawn-all cycles, guarantee a clearable spawn

        [Header("Tricky Round Settings (Level 3+)")]
        public bool enableTrickyRounds = false;

        public int spawnAllCountForTrickyRound = 5; // After this many spawn-all cycles, do a tricky round
    }

    /// <summary>
    /// Data about current spawning state
    /// </summary>
    public class SpawnState
    {
        private int _currentSpawnAllCount = 0;
        public int currentSpawnAllCount
        {
            get => _currentSpawnAllCount;
            set
            {
                OLogger.LogNotice($"CurrentSpawnAllCount changed {_currentSpawnAllCount} to {value}");
                _currentSpawnAllCount = value;
            }
        }
        public int currentLevel = 0;
        public MapAnalysisJobData lastAnalysisResults;
        public bool hasAnalysisResults = false;
        
        public bool shouldDoTrickyRound = false;
        public bool shouldGuaranteeClear = false;

        // Cached analysis results for performance
        public int cachedBestMaterialIndex = -1;
        public int cachedSecondBestMaterialIndex = -1;
        public bool cacheValid = false;

        public void Reset()
        {
            currentSpawnAllCount = 0;
            hasAnalysisResults = false;
            cacheValid = false;
            cachedBestMaterialIndex = -1;
            cachedSecondBestMaterialIndex = -1;
            if (lastAnalysisResults.IsValid)
                lastAnalysisResults.Dispose();
        }

        public void UpdateAnalysisResults(MapAnalysisJobData results)
        {
            if (!results.IsValid)
                return;

            if (lastAnalysisResults.IsValid)
                lastAnalysisResults.Dispose();

            // Copy the results
            var maxMaterialIndex = results.materialPixelCounts.Length - 1;
            lastAnalysisResults = MapAnalysisJobData.Create(maxMaterialIndex);
            results.materialPixelCounts.CopyTo(lastAnalysisResults.materialPixelCounts);
            results.materialSurfacePixelCounts.CopyTo(lastAnalysisResults.materialSurfacePixelCounts);
            results.materialPercentages.CopyTo(lastAnalysisResults.materialPercentages);
            results.totalNonEmptyPixels.CopyTo(lastAnalysisResults.totalNonEmptyPixels);

            hasAnalysisResults = true;
            cacheValid = false; // Invalidate cache when new data arrives

            // Log analysis results for debugging
            // var totalPixels = lastAnalysisResults.GetTotalNonEmptyPixels();
            // OLogger.Log($"Map Analysis Results - Total non-empty pixels: {totalPixels}");
            //
            // for (int i = 0; i < lastAnalysisResults.materialPixelCounts.Length; i++)
            // {
            //     var pixelCount = lastAnalysisResults.GetPixelCount(i);
            //     var surfaceCount = lastAnalysisResults.GetSurfacePixelCount(i);
            //     var percentage = lastAnalysisResults.GetPercentage(i);
            //
            //     if (pixelCount > 0)
            //     {
            //         OLogger.Log($"  Material {i} (): {pixelCount} pixels ({percentage:F1}%), {surfaceCount} surface pixels");
            //     }
            // }
        }
    }

    /// <summary>
    /// Result of shape spawn rule evaluation
    /// </summary>
    public struct ShapeSpawnResult
    {
        public List<BlockType> blockTypes; // Block types for each coordinate in the shape
        public bool isMultiColor; // Whether this shape has multiple colors
        public bool isGuaranteedClear; // Whether this spawn is designed to clear lines
        public bool isTrickyRound; // Whether this is a tricky round spawn
        public string debugInfo; // Debug information about why this spawn was chosen
    }

    /// <summary>
    /// System for determining how to spawn shapes based on level rules and map analysis
    /// </summary>
    public class ShapeSpawnRule
    {
        private readonly List<LevelSpawnConfig> _levelConfigs = new List<LevelSpawnConfig>();
        private readonly SpawnState _spawnState = new SpawnState();
        private PixelMaterials _materialDefinition;

        /// <summary>
        /// Initialize the spawn rule system with level configurations
        /// </summary>
        public ShapeSpawnRule(PixelMaterials materialDefinition)
        {
            _materialDefinition = materialDefinition;
            _levelConfigs.Clear();
            
            // Level 0: 4 colors, simple spawning
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 0,
                colorCount = 4,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103 },
                multiColorShapeChance = 0f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = false
            });

            // Level 1: 4 colors, low chance of multi-color shapes
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 1,
                colorCount = 4,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103 },
                multiColorShapeChance = 0.05f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = false
            });

            // Level 2: 5 colors, higher chance of multi-color shapes
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 2,
                colorCount = 5,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104 },
                multiColorShapeChance = 0.05f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = false
            });

            // Level 3: 5 colors, tricky rounds enabled
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 3,
                colorCount = 5,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104 },
                multiColorShapeChance = 0.05f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
            
            // Level 4: 6 colors, tricky rounds enabled
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 4,
                colorCount = 6,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105 },
                multiColorShapeChance = 0.05f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });

            // Level 5: 6 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 5,
                colorCount = 7,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105 },
                multiColorShapeChance = 0.08f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
            
            // Level 6: 7 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 6,
                colorCount = 7,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105, (BlockType)106 },
                multiColorShapeChance = 0.08f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
            
            // Level 7: 7 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 7,
                colorCount = 7,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105, (BlockType)106 },
                multiColorShapeChance = 0.1f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });

            // Level 8: 8 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 8,
                colorCount = 8,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105, (BlockType)106, (BlockType)107 },
                multiColorShapeChance = 0.08f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
            
            // Level 9: 8 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 9,
                colorCount = 8,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105, (BlockType)106, (BlockType)107 },
                multiColorShapeChance = 0.1f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
        }

        /// <summary>
        /// Update the current level
        /// </summary>
        public void SetLevel(int level)
        {
            if (_spawnState.currentLevel == level) return;
            _spawnState.currentLevel = level;
            _spawnState.currentSpawnAllCount = 0; // Reset spawn count when level changes
        }

        /// <summary>
        /// Update map analysis results
        /// </summary>
        public void UpdateMapAnalysis(MapAnalysisJobData analysisResults)
        {
            if (!analysisResults.IsValid)
            {
                // This is normal when game starts or no shapes have been placed yet
                return;
            }

            // UpdateAnalysisResults will handle copying and disposing
            _spawnState.UpdateAnalysisResults(analysisResults);
        }

        /// <summary>
        /// Called when SpawnAllShapes is invoked - handles spawn count logic and resets counters when needed
        /// </summary>
        public void OnSpawnAllShapes()
        {
            var config = GetCurrentLevelConfig();
            if (config == null)
                return;

            _spawnState.currentSpawnAllCount++;
            _spawnState.shouldGuaranteeClear = _spawnState.currentSpawnAllCount % config.spawnAllCountForGuaranteedClear == 0;
            _spawnState.shouldDoTrickyRound = config.enableTrickyRounds && _spawnState.currentSpawnAllCount % config.spawnAllCountForTrickyRound == 0;
        }

        /// <summary>
        /// Generate block types for a shape based on current rules and map state
        /// </summary>
        public ShapeSpawnResult GenerateShapeBlockTypes(int blockCount)
        {
            var config = GetCurrentLevelConfig();
            if (config == null)
            {
                // Fallback to simple random spawning
                return GenerateSimpleRandomShape(blockCount);
            }

            if (_spawnState.shouldDoTrickyRound)
            {
                return GenerateTrickyRoundShape(blockCount, config);
            }
            else if (_spawnState.shouldGuaranteeClear)
            {
                return GenerateGuaranteedClearShape(blockCount, config);
            }
            else
            {
                return GenerateNormalShape(blockCount, config);
            }
        }

        /// <summary>
        /// Reset the spawn state (call when game resets)
        /// </summary>
        public void Reset()
        {
            _spawnState.Reset();
            SetLevel(0);
        }

        private LevelSpawnConfig GetCurrentLevelConfig()
        {
            var config = _levelConfigs.FirstOrDefault(c => c.level == _spawnState.currentLevel);
            if (config != null) return config;
            
            // Fallback to the highest available level config
            config = _levelConfigs.LastOrDefault();
            if (config != null)
            {
                OLogger.LogWarning($"ShapeSpawnRule: No config for level {_spawnState.currentLevel}, using level {config.level}");
            }

            return config;
        }

        /// <summary>
        /// Cache the best materials for performance
        /// </summary>
        private void CacheBestMaterials(LevelSpawnConfig config)
        {
            if (_spawnState.cacheValid || !_spawnState.hasAnalysisResults) return;

            var materialSurfaceCounts = new List<(int configIndex, int surfaceCount)>();

            for (var i = 0; i < config.availableBlockTypes.Count; i++)
            {
                var materialIndex = (int)config.availableBlockTypes[i] - 100;
                if (materialIndex < 0 || materialIndex >= _spawnState.lastAnalysisResults.materialSurfacePixelCounts.Length) continue;
                
                var surfaceCount = _spawnState.lastAnalysisResults.GetSurfacePixelCount(materialIndex);
                materialSurfaceCounts.Add((i, surfaceCount));
            }

            materialSurfaceCounts.Sort((a, b) => b.surfaceCount.CompareTo(a.surfaceCount));

            _spawnState.cachedBestMaterialIndex = materialSurfaceCounts.Count > 0 ? materialSurfaceCounts[0].configIndex : -1;
            _spawnState.cachedSecondBestMaterialIndex = materialSurfaceCounts.Count > 1 ? materialSurfaceCounts[1].configIndex : -1;
            _spawnState.cacheValid = true;
        }

        /// <summary>
        /// Get debug information about current spawn state
        /// </summary>
        public string GetDebugInfo()
        {
            var config = GetCurrentLevelConfig();
            return $"Level: {_spawnState.currentLevel}, SpawnCount: {_spawnState.currentSpawnAllCount}, " +
                   $"HasAnalysis: {_spawnState.hasAnalysisResults}, Config: {(config != null ? $"L{config.level}" : "None")}, " +
                   $"Cache: {(_spawnState.cacheValid ? "Valid" : "Invalid")}";
        }

        private ShapeSpawnResult GenerateSimpleRandomShape(int blockCount)
        {
            var blockTypes = new List<BlockType>();
            var randomBlockType = (BlockType)Random.Range(100, 103); // Default to first 3 colors

            for (var i = 0; i < blockCount; i++)
            {
                blockTypes.Add(randomBlockType);
            }

            return new ShapeSpawnResult
            {
                blockTypes = blockTypes,
                isMultiColor = false,
                isGuaranteedClear = false,
                isTrickyRound = false,
                debugInfo = "Simple random (no level config)"
            };
        }

        private ShapeSpawnResult GenerateNormalShape(int blockCount, LevelSpawnConfig config)
        {
            var blockTypes = new List<BlockType>();

            // Decide if this should be a multicolor shape
            var isMultiColor = Random.value < config.multiColorShapeChance;

            if (isMultiColor && blockCount > 1)
            {
                // Generate a shape with 2 colors
                var color1 = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                var color2 = config.availableBlockTypes[Random.Range(0, config.colorCount)];

                // Make sure colors are different
                while (color2 == color1 && config.colorCount > 1)
                {
                    color2 = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                }

                // Split the shape roughly in half between the two colors
                var halfCount = blockCount / 2;
                for (var i = 0; i < blockCount; i++)
                {
                    blockTypes.Add(i < halfCount ? color1 : color2);
                }

                return new ShapeSpawnResult
                {
                    blockTypes = blockTypes,
                    isMultiColor = true,
                    isGuaranteedClear = false,
                    isTrickyRound = false,
                    debugInfo = $"Multi-color normal shape: {color1}, {color2}"
                };
            }
            else
            {
                // Generate a single-color shape
                var color = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                for (var i = 0; i < blockCount; i++)
                {
                    blockTypes.Add(color);
                }

                return new ShapeSpawnResult
                {
                    blockTypes = blockTypes,
                    isMultiColor = false,
                    isGuaranteedClear = false,
                    isTrickyRound = false,
                    debugInfo = $"Single-color normal shape: {color}"
                };
            }
        }

        private ShapeSpawnResult GenerateGuaranteedClearShape(int blockCount, LevelSpawnConfig config)
        {
            var blockTypes = new List<BlockType>();

            if (!_spawnState.hasAnalysisResults)
            {
                // No analysis data, fall back to normal shape
                return GenerateNormalShape(blockCount, config);
            }

            // Use cached the best material if available
            CacheBestMaterials(config);

            if (_spawnState.cachedBestMaterialIndex < 0)
            {
                // No valid material found, fall back to normal shape
                OLogger.LogWarning("GenerateGuaranteedClearShape: No valid material found!");
                return GenerateNormalShape(blockCount, config);
            }

            var bestBlockType = config.availableBlockTypes[_spawnState.cachedBestMaterialIndex];
            var materialIndex = _materialDefinition.GetIndex((PixelMaterialId)bestBlockType);
            var surfaceCount = _spawnState.lastAnalysisResults.GetSurfacePixelCount(materialIndex);

            // Generate shape with the best material for clearing
            for (var i = 0; i < blockCount; i++)
            {
                blockTypes.Add(bestBlockType);
            }
            
            _spawnState.shouldGuaranteeClear = false;

            return new ShapeSpawnResult
            {
                blockTypes = blockTypes,
                isMultiColor = false,
                isGuaranteedClear = true,
                isTrickyRound = false,
                debugInfo = $"Guaranteed clear shape: {bestBlockType}, materialIndex: {materialIndex} (surface pixels: {surfaceCount})"
            };
        }

        private ShapeSpawnResult GenerateTrickyRoundShape(int blockCount, LevelSpawnConfig config)
        {
            var blockTypes = new List<BlockType>();

            if (!_spawnState.hasAnalysisResults || blockCount < 2)
            {
                // No analysis data or shape too small, fall back to guaranteed clear
                return GenerateGuaranteedClearShape(blockCount, config);
            }

            // Use cached best materials
            CacheBestMaterials(config);

            if (_spawnState.cachedBestMaterialIndex < 0 || _spawnState.cachedSecondBestMaterialIndex < 0)
            {
                // Not enough materials, fall back to guaranteed clear
                return GenerateGuaranteedClearShape(blockCount, config);
            }

            var bestBlockType = config.availableBlockTypes[_spawnState.cachedBestMaterialIndex];
            var secondBlockType = config.availableBlockTypes[_spawnState.cachedSecondBestMaterialIndex];

            var bestMaterialIndex = _materialDefinition.GetIndex((PixelMaterialId)bestBlockType);
            var secondMaterialIndex = _materialDefinition.GetIndex((PixelMaterialId)secondBlockType);
            var bestSurfaceCount = _spawnState.lastAnalysisResults.GetSurfacePixelCount(bestMaterialIndex);
            var secondSurfaceCount = _spawnState.lastAnalysisResults.GetSurfacePixelCount(secondMaterialIndex);

            // Split shape half-and-half between the two materials
            var halfCount = blockCount / 2;
            for (var i = 0; i < blockCount; i++)
            {
                blockTypes.Add(i < halfCount ? bestBlockType : secondBlockType);
            }
            
            _spawnState.shouldDoTrickyRound = false;

            return new ShapeSpawnResult
            {
                blockTypes = blockTypes,
                isMultiColor = true,
                isGuaranteedClear = false,
                isTrickyRound = true,
                debugInfo = $"Tricky round shape: {bestBlockType} ({bestSurfaceCount}) + {secondBlockType} ({secondSurfaceCount})"
            };
        }
    }
}