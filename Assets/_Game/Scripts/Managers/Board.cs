using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using PrimeTween;
using UnityEngine;
using Unity.Jobs;
using Unity.Collections;

namespace OP.BlockSand
{
    public class Board : MonoBehaviour
    {
        [SerializeField]
        private PixelMaterials _materialDefinition;
        
        [SerializeField]
        private ClearAnimationSystem _clearAnimationSystem;

        [SerializeField]
        private SpriteRenderer _inDangerWarningRenderer;

        [SerializeField, Sirenix.OdinInspector.ReadOnly]
        private Rect _actualBoardRect;
        [SerializeField, Sirenix.OdinInspector.ReadOnly]
        private Rect _drawableBoardRect;
        
        private SandWorld _sandWorld;

        private int _worldWidth;
        private int _worldHeight;
        private int _inDangerThresholdY;
        private int _loseThresholdY;
        private int _minValidX; // After left boundary
        private int _maxValidX; // Before right boundary

        [Header("Game Settings")]
        [SerializeField] private int _pixelSleepThreshold = 15; // Minimum sleep counter for stable clustering (default: 15, max: 30)

        [Header("Debug")]
        [SerializeField, Sirenix.OdinInspector.ReadOnly] private bool _isGameLost = false;
        [SerializeField, Sirenix.OdinInspector.ReadOnly] private int _activeClusters = 0;
        [SerializeField, Sirenix.OdinInspector.ReadOnly] private GameStateCheckStatus _checkStatus = GameStateCheckStatus.Idle;

        // Timing to prevent race conditions
        private float _lastDrawTime = 0f;
        private const float DRAW_TO_CHECK_DELAY = 0.1f; // Wait 100ms after drawing before checking game state

        public Rect DrawableRect => _drawableBoardRect;

        // Map analysis results - accessible by ShapeSpawner
        private MapAnalysisJobData _lastMapAnalysisResults;
        public bool HasMapAnalysisResults => _lastMapAnalysisResults.IsValid;

        /// <summary>
        /// Get a copy of map analysis results to avoid disposal issues
        /// </summary>
        public MapAnalysisJobData GetMapAnalysisResults() => _lastMapAnalysisResults;

        // Track placed shapes and their materials
        // We track by material INDEX (not ID) because that's what pixels store
        private readonly HashSet<int> _activeMaterialIndices = new ();
        private readonly Dictionary<int, int> _materialIndexPixelCounts = new ();

        // For legacy/debug purposes - track material IDs that have been placed
        private readonly List<PixelMaterialId> _placedMaterialIds = new ();

        // Helper mapping to convert Material ID -> Material Index
        private readonly Dictionary<PixelMaterialId, int> _materialIdToIndexMap = new ();

        // Job system state
        private JobHandle _currentJobHandle;
        private GameStateJobData _currentJobData;
        private readonly List<Vector2Int> _pixelsToAnimate = new List<Vector2Int>();

        public async UniTask InitAsync(SandWorld sandWorld)
        {
            _sandWorld = sandWorld;

            _worldWidth = _sandWorld.Width;
            _worldHeight = _sandWorld.Height;
            _inDangerThresholdY = _worldWidth - 10;
            _loseThresholdY = _worldWidth + 5;
            _minValidX = 1; // After left boundary
            _maxValidX = _worldWidth - 2; // Before right boundary

            _clearAnimationSystem.Initialize(_sandWorld.PixelWorld, _materialDefinition);
            
            _inDangerWarningRenderer.enabled = false;

            SetupBoardBounds();

            // Build material ID to index mapping
            BuildMaterialIdToIndexMap();
            
            this.EventSubscribe<OnGameViewChangedEvent>(HandleGameViewChanged);
            
            await LoadSavedBoardAsync(DataShortcut.Board);
        }

        private async UniTask LoadSavedBoardAsync(BoardData data)
        {
            if (data == null || data.activePixels == null || data.activePixels.Count == 0)
                return;
            
            foreach (var pixel in data.activePixels)
            {
                DrawAt(new Vector2(pixel.x, pixel.y), new Color32(pixel.r, pixel.g, pixel.b, pixel.a), GetMaterialId(pixel.materialIndex));
            }
            await UniTask.DelayFrame(5);
        }

        private void SetupBoardBounds()
        {
            // Need to minus 2 because of the margin, the level already include border 1px
            var boardPosition = (Vector2)_sandWorld.transform.position;
            var drawableBoardWidth = (float)(_sandWorld.Width - 2) / _sandWorld.PixelsPerUnit;
            var drawableBoardHeight = (float)(_sandWorld.Height - 2) / _sandWorld.PixelsPerUnit;
            _drawableBoardRect = new Rect(boardPosition.x - drawableBoardWidth / 2, boardPosition.y - drawableBoardHeight / 2, drawableBoardWidth, drawableBoardHeight);
            
            var boardWidth = (float)(_sandWorld.Width) / _sandWorld.PixelsPerUnit;
            var boardHeight = (float)(_sandWorld.Height) / _sandWorld.PixelsPerUnit;
            _actualBoardRect = new Rect(boardPosition.x - boardWidth / 2, boardPosition.y - boardHeight / 2, boardWidth, boardHeight);
        }
        
        private void HandleGameViewChanged(OnGameViewChangedEvent e)
        {
            SetupBoardBounds();
        }

        private void BuildMaterialIdToIndexMap()
        {
            _materialIdToIndexMap.Clear();

            if (_materialDefinition?.Materials != null)
            {
                // OLogger.Log($"Building material ID to index mapping from {_materialDefinition.Materials.Length} materials:");
                for (int i = 0; i < _materialDefinition.Materials.Length; i++)
                {
                    var material = _materialDefinition.Materials[i];
                    _materialIdToIndexMap[material.id] = i;
                    // OLogger.Log($"  Material {material.id} -> index {i}");
                }
            }
            else
            {
                OLogger.LogError("Material definition is null or has no materials!");
            }
        }

        /// <summary>
        /// Get material index from material ID. Returns -1 if not found.
        /// </summary>
        private int GetMaterialIndex(PixelMaterialId materialId)
        {
            if (_materialIdToIndexMap.TryGetValue(materialId, out int index))
            {
                return index;
            }

            OLogger.LogError($"Material ID {materialId} not found in mapping! Available: [{string.Join(", ", _materialIdToIndexMap.Keys)}]");
            return -1;
        }

        /// <summary>
        /// Get material ID from material index. Returns Empty if not found.
        /// </summary>
        private PixelMaterialId GetMaterialId(int materialIndex)
        {
            foreach (var kvp in _materialIdToIndexMap)
            {
                if (kvp.Value == materialIndex)
                    return kvp.Key;
            }
            return PixelMaterialId.Empty;
        }

        public bool ValidateDrawShape(Shape shape, out Vector3 targetShapePosition, out Vector2 pixelPosition)
        {
            targetShapePosition = Vector3.zero;
            pixelPosition = Vector2.zero;
            
            var shapePointBottomLeft = new Vector3(
                shape.OTransform.position.x - shape.Rect.width / 2f, 
                shape.OTransform.position.y - shape.Rect.height / 2f);
            var shapePointBottomLeftInPixel = GetPixelPositionInBoard(shapePointBottomLeft);
            
            if (shapePointBottomLeftInPixel.x < 1f || shapePointBottomLeftInPixel.y < 1f)
                return false;
            
            // Check if overlap other pixels
            foreach (var blockPixel in shape.GetPixels())
            {
                var pixelPos = new Vector3(shapePointBottomLeftInPixel.x + blockPixel.coordinate.x, shapePointBottomLeftInPixel.y + blockPixel.coordinate.y, 0);
                if (_sandWorld.PixelWorld.TryGetPixelAt(pixelPos, out var pixel))
                {
                    if (pixel.materialIndex != 0)
                        return false;
                }
                else
                    return false;
            }

            targetShapePosition = shape.OTransform.position - shapePointBottomLeft + GetWorldPositionFromPixel(shapePointBottomLeftInPixel);
            pixelPosition = shapePointBottomLeftInPixel;
            return true;
        }
        
        public void DrawShape(Shape shape, Vector2 pixelPosition)
        {
            // Wait for any running game state check jobs to complete before drawing
            // This prevents race condition with job system accessing pixel buffer
            if (_checkStatus != GameStateCheckStatus.Idle)
            {
                // Stop any ongoing animation first
                if (_clearAnimationSystem != null && _clearAnimationSystem.IsAnimating)
                {
                    _clearAnimationSystem.StopAnimation();
                }

                // Complete and cleanup job system
                CleanupJobSystem();
                _checkStatus = GameStateCheckStatus.Idle;
            }

            // Track materials used in this shape
            var shapeMaterials = new HashSet<PixelMaterialId>();

            foreach (var blockPixel in shape.GetPixels())
            {
                var pixelPos = new Vector3(pixelPosition.x + blockPixel.coordinate.x, pixelPosition.y + blockPixel.coordinate.y, 0);
                var materialId = (PixelMaterialId)(int)blockPixel.blockType;

                // Get material index from ID
                var materialIndex = GetMaterialIndex(materialId);
                if (materialIndex < 0) continue;
                // OLogger.Log($"Drawing pixel at {pixelPos} with blockType {blockPixel.blockType} -> materialId {materialId} -> index {materialIndex}");
                DrawAt(pixelPos, blockPixel.color, materialId);

                // Track this material by index (exclude Empty material)
                if (materialId != PixelMaterialId.Empty && materialIndex > 0)
                {
                    _activeMaterialIndices.Add(materialIndex);
                }

                // Update pixel count for this material index
                if (!_materialIndexPixelCounts.ContainsKey(materialIndex))
                    _materialIndexPixelCounts[materialIndex] = 0;
                _materialIndexPixelCounts[materialIndex]++;

                // Track this material ID for legacy compatibility
                shapeMaterials.Add(materialId);
                // Error already logged in GetMaterialIndex()
            }

            // Add new materials to active set (for legacy compatibility)
            foreach (var materialId in shapeMaterials)
            {
                if (!_placedMaterialIds.Contains(materialId))
                {
                    _placedMaterialIds.Add(materialId);
                    // OLogger.Log($"New material placed: {materialId}");
                }
            }

            // Record the time when we drew pixels to prevent immediate game state check
            _lastDrawTime = Time.time;

            // Note: Game state check should be called from GameManager Update loop
        }

        private void DrawAt(Vector2 pixelPos, Color32 color, PixelMaterialId materialId)
        {
            _sandWorld.PixelWorld.DrawPixelAt(pixelPos.x, pixelPos.y, color.r, color.g, color.b, color.a, materialId);
        }

        private Vector3 GetPixelPositionInBoard(Vector3 worldPosition)
        {
            var localPositionInBoard = _sandWorld.transform.InverseTransformPoint(worldPosition);
            var positionFromBottomLeft = new Vector3(localPositionInBoard.x + _actualBoardRect.width / 2f, localPositionInBoard.y + _actualBoardRect.height / 2f, 0);
            return _sandWorld.PixelWorld.WorldToPixelPos(positionFromBottomLeft);
        }
        
        public Vector3 GetWorldPositionFromPixel(Vector3 pixelPosition)
        {
            var flooredPixelPosition = new Vector3(Mathf.Floor(pixelPosition.x), Mathf.Floor(pixelPosition.y), 0);
            var localPositionInBoardFromBottomLeft = _sandWorld.PixelWorld.PixelToWorldPos(flooredPixelPosition);
            var localPositionInBoard = new Vector3(localPositionInBoardFromBottomLeft.x - _actualBoardRect.width / 2f, localPositionInBoardFromBottomLeft.y - _actualBoardRect.height / 2f, 0);
            return _sandWorld.transform.TransformPoint(localPositionInBoard);
        }

        /// <summary>
        /// Check for clearable sand clusters and lose condition
        /// Call this from GameManager Update loop
        /// </summary>
        public void CheckGameState()
        {
            if (_isGameLost || _checkStatus != GameStateCheckStatus.Idle)
                return;

            // Wait for any pending pixels to be drawn before checking game state
            // This prevents race condition with PixelWorld.copyPixelScheduledForDrawing()
            if (HasPendingPixelsToDraw())
            {
                OLogger.LogWarning("CheckGameState: Waiting for pending pixels to be drawn");
                return;
            }

            // OLogger.Log("CheckGameState: Starting lose check");
            StartLoseCheck();
        }

        /// <summary>
        /// Update game state checking process - call this from GameManager Update
        /// </summary>
        public void UpdateGameStateCheck()
        {
            switch (_checkStatus)
            {
                case GameStateCheckStatus.CheckingLose:
                    UpdateLoseCheck();
                    break;

                case GameStateCheckStatus.CheckingClusters:
                    UpdateClusterCheck();
                    break;

                case GameStateCheckStatus.ClearingClusters:
                    // ClearAnimationSystem handles the animation, we just wait
                    // The animation completion callback will reset status to Idle
                    break;

                case GameStateCheckStatus.AnalyzingMap:
                    UpdateMapAnalysis();
                    break;
            }
        }

        private void StartLoseCheck()
        {
            var pixelBuffer = GetPixelBuffer();
            if (!pixelBuffer.IsCreated)
                return;

            // Dispose previous job data if exists
            if (_currentJobData.IsValid)
                _currentJobData.Dispose();

            // Initialize job data with Persistent allocator to avoid TempJob lifetime issues
            _currentJobData = new GameStateJobData
            {
                hasLost = new NativeArray<bool>(1, Allocator.Persistent),
                inDanger = new NativeArray<bool>(1, Allocator.Persistent),
                firstLosePixelX = new NativeArray<int>(1, Allocator.Persistent),
                firstLosePixelY = new NativeArray<int>(1, Allocator.Persistent),
                pixelBufferCopy = pixelBuffer // Store the copy in job data
            };

            var loseCheckJob = new LoseCheckJob()
            {
                worldWidth = _worldWidth,
                worldHeight = _worldHeight,
                inDangerThresholdY = _inDangerThresholdY,
                loseThresholdY = _loseThresholdY,
                pixelBuffer = _currentJobData.pixelBufferCopy,
                hasLost = _currentJobData.hasLost,
                inDanger = _currentJobData.inDanger,
                firstLosePixelX = _currentJobData.firstLosePixelX,
                firstLosePixelY = _currentJobData.firstLosePixelY
            };

            _currentJobHandle = loseCheckJob.Schedule();
            _checkStatus = GameStateCheckStatus.CheckingLose;
        }

        private void UpdateLoseCheck()
        {
            if (!_currentJobHandle.IsCompleted)
                return;

            // Complete the job handle first
            _currentJobHandle.Complete();

            try
            {
                // Check if we're in danger
                UpdateInDanger(_currentJobData.inDanger[0]);

                if (_currentJobData.hasLost[0])
                {
                    _isGameLost = true;

                    // Cleanup and return to idle
                    _currentJobData.Dispose();
                    _checkStatus = GameStateCheckStatus.Idle;

                    Core.Lose();
                    return;
                }

                // No lose condition, proceed to cluster check
                StartClusterCheck();
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error in UpdateLoseCheck: {e.Message}");
                // Cleanup and return to idle on error
                if (_currentJobData.IsValid)
                    _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
            }
        }

        private void UpdateInDanger(bool inDanger)
        {
            switch (inDanger)
            {
                case true when !_inDangerWarningRenderer.enabled:
                    _inDangerWarningRenderer.enabled = true;
                    Tween.StopAll(_inDangerWarningRenderer);
                    Tween.Alpha(_inDangerWarningRenderer, 0f, 1f, 0.5f, Ease.InOutSine, cycles: -1, CycleMode.Yoyo);
                    break;
                case false when _inDangerWarningRenderer.enabled:
                    Tween.StopAll(_inDangerWarningRenderer);
                    _inDangerWarningRenderer.enabled = false;
                    break;
            }
        }

        private void StartClusterCheck()
        {
            var pixelBuffer = GetPixelBuffer();
            if (!pixelBuffer.IsCreated)
            {
                OLogger.LogWarning("StartClusterCheck: No valid pixel buffer available");
                // Cleanup any existing job data before returning to idle
                if (_currentJobData.IsValid)
                    _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
                return;
            }

            // Skip if no materials have been placed yet
            if (_activeMaterialIndices.Count == 0)
            {
                // OLogger.LogWarning("StartClusterCheck: No active materials to check");
                // Cleanup any existing job data before returning to idle
                if (_currentJobData.IsValid)
                    _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
                return;
            }

            // OLogger.Log($"StartClusterCheck: Checking {_activeMaterialIndices.Count} active material indices: [{string.Join(", ", _activeMaterialIndices)}]");
            // OLogger.Log($"StartClusterCheck: World size {_worldWidth}x{_worldHeight}, valid X range: {_minValidX}-{_maxValidX}");
            // OLogger.Log($"Active material indices count: {_activeMaterialIndices.Count}");

            try
            {
                // Convert active material indices to NativeArray with Persistent allocator
                var activeMaterialIndicesArray = new NativeArray<int>(_activeMaterialIndices.Count, Allocator.Persistent);
                int index = 0;
                foreach (var materialIndex in _activeMaterialIndices)
                {
                    activeMaterialIndicesArray[index] = materialIndex;
                    // OLogger.Log($"Adding active material index to job: {materialIndex}");
                    index++;
                }

                // Prepare job data with Persistent allocator to avoid TempJob lifetime issues
                int totalPixels = _worldWidth * _worldHeight;
                _currentJobData.clusterMap = new NativeArray<int>(totalPixels, Allocator.Persistent);
                _currentJobData.clusters = new NativeArray<ClusterData>(totalPixels, Allocator.Persistent);
                _currentJobData.clearableClusters = new NativeArray<int>(totalPixels, Allocator.Persistent);
                _currentJobData.clusterCount = new NativeArray<int>(1, Allocator.Persistent);
                _currentJobData.clearableCount = new NativeArray<int>(1, Allocator.Persistent);
                _currentJobData.activeMaterialIndices = activeMaterialIndicesArray; // Now using material indices
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error preparing cluster check job data: {e.Message}");
                // Cleanup and return to idle
                if (_currentJobData.IsValid)
                    _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
                return;
            }

            var floodFillJob = new FloodFillJob
            {
                worldWidth = _worldWidth,
                worldHeight = _worldHeight,
                minValidX = _minValidX,
                maxValidX = _maxValidX,
                pixelBuffer = _currentJobData.pixelBufferCopy,
                activeMaterialIndices = _currentJobData.activeMaterialIndices,
                clusterMap = _currentJobData.clusterMap,
                clusters = _currentJobData.clusters,
                clearableClusters = _currentJobData.clearableClusters,
                clusterCount = _currentJobData.clusterCount,
                clearableCount = _currentJobData.clearableCount,
                sleepThreshold = _pixelSleepThreshold // Configurable threshold for stable clustering
            };

            _currentJobHandle = floodFillJob.Schedule();
            _checkStatus = GameStateCheckStatus.CheckingClusters;
        }

        private void UpdateClusterCheck()
        {
            if (!_currentJobHandle.IsCompleted)
                return;

            // Complete the job handle first
            _currentJobHandle.Complete();

            // Process results
            int foundClusters = _currentJobData.clusterCount[0];
            int clearableClusterCount = _currentJobData.clearableCount[0];

            _activeClusters = foundClusters;

            // OLogger.Log($"UpdateClusterCheck: Found {foundClusters} total clusters, {clearableClusterCount} clearable clusters");

            // Debug: Log cluster details
            // for (int i = 0; i < foundClusters && i < _currentJobData.clusters.Length; i++)
            // {
            //     var cluster = _currentJobData.clusters[i];
            //     var materialId = GetMaterialId(cluster.materialIndex);
            //     OLogger.Log($"  Cluster {i}: MaterialIndex {cluster.materialIndex} (ID: {materialId}), MinX={cluster.minX}, MaxX={cluster.maxX}, SpansLeftToRight={cluster.spansFromLeftToRight}");
            // }

            if (clearableClusterCount > 0)
            {
                // OLogger.Log($"Starting cluster clearing for {clearableClusterCount} clusters");
                StartClusterClearing(clearableClusterCount);
            }
            else
            {
                // OLogger.Log("No clusters to clear, starting map analysis");
                // No clusters to clear, proceed to map analysis
                StartMapAnalysis();
            }
        }

        private void StartClusterClearing(int clearableClusterCount)
        {
            // Collect all pixels to animate
            _pixelsToAnimate.Clear();
            var materialIndexCounts = new Dictionary<int, int>(); // Track by material INDEX

            // OLogger.Log($"Processing {clearableClusterCount} clearable clusters...");

            // Copy data from NativeArrays before they get disposed
            var clearableClusterIds = new List<int>();
            var clusterDataList = new List<ClusterData>();
            var clusterMapCopy = new int[_currentJobData.clusterMap.Length];

            for (var i = 0; i < clearableClusterCount; i++)
            {
                var clusterId = _currentJobData.clearableClusters[i];
                var cluster = _currentJobData.clusters[clusterId];
                clearableClusterIds.Add(clusterId);
                clusterDataList.Add(cluster);
            }

            // Copy cluster map data
            _currentJobData.clusterMap.CopyTo(clusterMapCopy);

            // Dispose JobData immediately after copying to prevent memory leak
            _currentJobData.Dispose();

            // Process clusters using copied data
            for (var i = 0; i < clearableClusterCount; i++)
            {
                var clusterId = clearableClusterIds[i];
                var cluster = clusterDataList[i];

                // Cluster now stores materialIndex directly
                var materialIndex = cluster.materialIndex;

                // OLogger.Log($"Processing clearable cluster {i}: ClusterId={clusterId}, MaterialIndex={materialIndex}, PixelCount={cluster.pixelCount}");

                // Collect pixels in this cluster
                var pixelsInThisCluster = 0;
                for (var y = 0; y < _worldHeight; y++)
                {
                    for (var x = 0; x < _worldWidth; x++)
                    {
                        var index = y * _worldWidth + x;
                        if (clusterMapCopy[index] != clusterId) continue;

                        _pixelsToAnimate.Add(new Vector2Int(x, y));
                        pixelsInThisCluster++;

                        // Count materials being cleared by INDEX
                        materialIndexCounts.TryAdd(materialIndex, 0);
                        materialIndexCounts[materialIndex]++;
                    }
                }

                // OLogger.Log($"Cluster {clusterId}: Found {pixelsInThisCluster} pixels, Expected: {cluster.pixelCount}, MaterialIndex: {materialIndex}");

                if (pixelsInThisCluster != cluster.pixelCount)
                {
                    // OLogger.LogError($"Mismatch! Cluster {clusterId} expected {cluster.pixelCount} pixels but found {pixelsInThisCluster}");
                }
            }

            // OLogger.Log($"Total pixels to animate: {_pixelsToAnimate.Count}");

            if (_pixelsToAnimate.Count > 0)
            {
                // OLogger.Log($"Clearing {clearableClusterCount} clusters with {_pixelsToAnimate.Count} pixels");

                // Force stop pixel movement for all pixels in clusters before animation
                // ForceStopPixelMovementForClusters(_pixelsToAnimate);

                // Update material counts
                UpdateMaterialCountsAfterClear(materialIndexCounts);

                // Use ClearAnimationSystem for animation
                _checkStatus = GameStateCheckStatus.ClearingClusters;
                _clearAnimationSystem.StartClearAnimation(_pixelsToAnimate, () =>
                {
                    // Animation complete callback - start map analysis
                    _pixelsToAnimate.Clear();
                    // OLogger.Log("Cluster clearing animation completed, starting map analysis");
                    StartMapAnalysis();
                });
            }
            else
            {
                // No pixels to clear, return to idle (JobData already disposed)
                _checkStatus = GameStateCheckStatus.Idle;
            }
        }

        // Animation methods removed - now using ClearAnimationSystem

        private void UpdateMaterialCountsAfterClear(Dictionary<int, int> clearedIndexCounts)
        {
            foreach (var kvp in clearedIndexCounts)
            {
                var materialIndex = kvp.Key;
                var clearedCount = kvp.Value;

                if (_materialIndexPixelCounts.ContainsKey(materialIndex))
                {
                    _materialIndexPixelCounts[materialIndex] -= clearedCount;
                    // OLogger.Log($"Material index {materialIndex}: {_materialIndexPixelCounts[materialIndex] + clearedCount} -> {_materialIndexPixelCounts[materialIndex]} pixels");

                    // Remove material from active set if no pixels left
                    if (_materialIndexPixelCounts[materialIndex] <= 0)
                    {
                        _materialIndexPixelCounts.Remove(materialIndex);
                        _activeMaterialIndices.Remove(materialIndex);

                        // Also remove from placed material IDs
                        var materialIdToRemove = GetMaterialId(materialIndex);
                        if (materialIdToRemove != PixelMaterialId.Empty)
                        {
                            _placedMaterialIds.Remove(materialIdToRemove);
                        }

                        // OLogger.Log($"Material index {materialIndex} completely cleared from board");
                    }
                }
                else
                {
                    OLogger.LogWarning($"Tried to clear material index {materialIndex} but it's not in pixel counts");
                }
            }
        }

        private void StartMapAnalysis()
        {
            // pixelBufferCopy should already be available from previous job steps
            if (!_currentJobData.pixelBufferCopy.IsCreated)
            {
                // If no pixel buffer copy, try to get one
                var pixelBuffer = GetPixelBuffer();
                if (!pixelBuffer.IsCreated)
                {
                    // No pixel buffer, return to idle
                    _currentJobData.Dispose();
                    _checkStatus = GameStateCheckStatus.Idle;
                    return;
                }
                _currentJobData.pixelBufferCopy = pixelBuffer;
            }

            // Initialize map analysis data
            var maxMaterialIndex = _materialDefinition.Materials.Length - 1;
            _currentJobData.mapAnalysisData = MapAnalysisJobData.Create(maxMaterialIndex);

            var mapAnalysisJob = new MapAnalysisJob
            {
                worldWidth = _worldWidth,
                worldHeight = _worldHeight,
                pixelBuffer = _currentJobData.pixelBufferCopy,
                maxMaterialIndex = maxMaterialIndex,
                materialPixelCounts = _currentJobData.mapAnalysisData.materialPixelCounts,
                materialSurfacePixelCounts = _currentJobData.mapAnalysisData.materialSurfacePixelCounts,
                materialPercentages = _currentJobData.mapAnalysisData.materialPercentages,
                totalNonEmptyPixels = _currentJobData.mapAnalysisData.totalNonEmptyPixels
            };

            _currentJobHandle = mapAnalysisJob.Schedule();
            _checkStatus = GameStateCheckStatus.AnalyzingMap;
        }

        private void UpdateMapAnalysis()
        {
            if (!_currentJobHandle.IsCompleted)
                return;

            // Complete the job handle first
            _currentJobHandle.Complete();

            try
            {
                // Process map analysis results
                ProcessMapAnalysisResults();
            }
            finally
            {
                // Always cleanup job data, even if processing fails
                if (_currentJobData.mapAnalysisData.IsValid)
                    _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
            }
        }

        private void ProcessMapAnalysisResults()
        {
            if (!_currentJobData.mapAnalysisData.IsValid)
                return;

            // Dispose previous results if any
            if (_lastMapAnalysisResults.IsValid)
                _lastMapAnalysisResults.Dispose();

            // Copy results for later use by ShapeSpawner
            var maxMaterialIndex = _materialDefinition.Materials.Length - 1;
            _lastMapAnalysisResults = MapAnalysisJobData.Create(maxMaterialIndex);

            // Copy data from job results
            _currentJobData.mapAnalysisData.materialPixelCounts.CopyTo(_lastMapAnalysisResults.materialPixelCounts);
            _currentJobData.mapAnalysisData.materialSurfacePixelCounts.CopyTo(_lastMapAnalysisResults.materialSurfacePixelCounts);
            _currentJobData.mapAnalysisData.materialPercentages.CopyTo(_lastMapAnalysisResults.materialPercentages);
            _currentJobData.mapAnalysisData.totalNonEmptyPixels.CopyTo(_lastMapAnalysisResults.totalNonEmptyPixels);

            // Log analysis results for debugging
            // var totalPixels = _lastMapAnalysisResults.GetTotalNonEmptyPixels();
            // OLogger.Log($"Map Analysis Results - Total non-empty pixels: {totalPixels}");
            //
            // for (int i = 0; i < _lastMapAnalysisResults.materialPixelCounts.Length; i++)
            // {
            //     var pixelCount = _lastMapAnalysisResults.GetPixelCount(i);
            //     var surfaceCount = _lastMapAnalysisResults.GetSurfacePixelCount(i);
            //     var percentage = _lastMapAnalysisResults.GetPercentage(i);
            //
            //     if (pixelCount > 0)
            //     {
            //         var materialId = GetMaterialId(i);
            //         OLogger.Log($"  Material {i} ({materialId}): {pixelCount} pixels ({percentage:F1}%), {surfaceCount} surface pixels");
            //     }
            // }
            
            // Notify ShapeSpawner of new analysis results
            Core.Event.Fire(new OnMapAnalysisJobFinishedEvent() { analysisResults = _lastMapAnalysisResults });
        }

        private NativeArray<Pixel> GetPixelBuffer()
        {
            // This method needs to collect all pixels from the world into a single buffer
            // For the flood fill and lose check algorithms to work properly
            if (_sandWorld?.PixelWorld?.ActiveChunks == null || _sandWorld.PixelWorld.ActiveChunks.Count == 0)
                return default;

            // For single chunk mode (AlwaysShowFullChunk = true), create a copy to avoid race conditions
            if (_sandWorld.PixelWorld.AlwaysShowFullChunk && _sandWorld.PixelWorld.ActiveChunks.Count > 0)
            {
                var firstChunk = _sandWorld.PixelWorld.ActiveChunks[0];
                if (firstChunk.LoadSucceeded && firstChunk.Pixels.IsCreated)
                {
                    // Create a copy of the pixel buffer to avoid race conditions with job system
                    // Use Persistent allocator since this will be stored in GameStateJobData
                    var pixelBufferCopy = new NativeArray<Pixel>(firstChunk.Pixels.Length, Allocator.Persistent);
                    firstChunk.Pixels.CopyTo(pixelBufferCopy);
                    return pixelBufferCopy;
                }
            }
            return default;
        }

        /// <summary>
        /// Reset game state
        /// </summary>
        public void ResetGameState()
        {
            _isGameLost = false;
            _activeClusters = 0;
            _clearAnimationSystem?.StopAnimation();

            // Reset material tracking
            _activeMaterialIndices.Clear();
            _materialIndexPixelCounts.Clear();
            _placedMaterialIds.Clear();
            // Don't clear _materialIdToIndexMap as it's built from material definition

            // Cleanup map analysis results
            if (_lastMapAnalysisResults.IsValid)
                _lastMapAnalysisResults.Dispose();

            // Cleanup job system safely
            CleanupJobSystem();

            _checkStatus = GameStateCheckStatus.Idle;
            _pixelsToAnimate.Clear();

            OLogger.Log("Game state and material tracking reset");
        }

        /// <summary>
        /// Safely cleanup job system to prevent memory leaks
        /// </summary>
        private void CleanupJobSystem()
        {
            try
            {
                // Complete any running job
                _currentJobHandle.Complete();
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error completing job handle: {e.Message}");
            }

            try
            {
                // Dispose job data
                if (_currentJobData.IsValid)
                {
                    _currentJobData.Dispose();
                }
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error disposing job data: {e.Message}");
            }

            try
            {
                // Dispose map analysis results
                if (_lastMapAnalysisResults.IsValid)
                {
                    _lastMapAnalysisResults.Dispose();
                }
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error disposing map analysis results: {e.Message}");
            }

            // Reset job handle
            _currentJobHandle = default;
        }

        /// <summary>
        /// Called when Board is being destroyed
        /// </summary>
        private void OnDestroy()
        {
            // Cleanup map analysis results
            if (_lastMapAnalysisResults.IsValid)
                _lastMapAnalysisResults.Dispose();

            CleanupJobSystem();

            this.EventUnsubscribe<OnGameViewChangedEvent>(HandleGameViewChanged);
        }

        /// <summary>
        /// Called when Board is being disabled
        /// </summary>
        private void OnDisable()
        {
            CleanupJobSystem();
        }

        /// <summary>
        /// Check if we should trigger a new game state check
        /// Call this from GameManager when appropriate (e.g., after physics settle)
        /// </summary>
        public bool ShouldCheckGameState()
        {
            // Don't check if game is lost or already checking
            if (_isGameLost || _checkStatus != GameStateCheckStatus.Idle)
                return false;

            // Don't check if there are pending pixels to draw
            if (HasPendingPixelsToDraw())
                return false;

            // Don't check immediately after drawing - wait for pixels to settle
            return !(Time.time - _lastDrawTime < DRAW_TO_CHECK_DELAY);
        }

        /// <summary>
        /// Check if there are pixels waiting to be drawn to the world
        /// This helps prevent race conditions with job system
        /// </summary>
        private bool HasPendingPixelsToDraw()
        {
            // Check if there are pixels waiting to be drawn
            return _sandWorld.PixelWorld._pixelsToDraw.Count > 0 ||
                   _sandWorld.PixelWorld._pixelsToWakeUp.Count > 0;
        }

        /// <summary>
        /// Force stop pixel movement for all pixels in clusters to ensure they are stable before clearing animation
        /// </summary>
        private void ForceStopPixelMovementForClusters(List<Vector2Int> pixelPositions)
        {
            if (pixelPositions == null || pixelPositions.Count == 0)
                return;

            OLogger.Log($"Force stopping movement for {pixelPositions.Count} pixels before clearing animation");

            // Convert Vector2Int to Vector2 for the PixelWorld method
            var positions = new List<Vector2>(pixelPositions.Count);
            foreach (var pixelPos in pixelPositions)
            {
                positions.Add(new Vector2(pixelPos.x, pixelPos.y));
            }

            // Force stop movement for all pixels
            _sandWorld.PixelWorld.ForceStopPixelMovement(positions);
        }
    }
}