using System.Collections.Generic;
using System.Linq;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Services.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace OP.BlockSand
{
    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;
        [SerializeField]
        private PixelMaterials _materialDefinition;

        [SerializeField]
        private Transform _shapeContainer;

        [SerializeField]
        private Transform[] _spawnPoints;

        private readonly Shape[] _shapes = new Shape[3];
        
        private ShapeSpawnRule _spawnRule;

        public void Init()
        {
            _spawnRule = new ShapeSpawnRule(_materialDefinition);
            var currentLevel = DataShortcut.Score.level;
            _spawnRule.SetLevel(currentLevel);

            LoadSavedShapes(DataShortcut.Board.savedShapes);

            // Subscribe to score level up events to update spawn rules
            this.EventSubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
            this.EventSubscribe<OnMapAnalysisJobFinishedEvent>(HandleMapAnalysisFinished);
        }

        public void LoadSavedShapes(List<BoardData.Shape> savedShapes)
        {
            if (savedShapes == null || savedShapes.Count == 0)
            {
                SpawnAllShapes();
                return;
            }

            for (var i = 0; i < savedShapes.Count; i++)
            {
                if (savedShapes[i] == null) continue;
                _shapes[i] = SpawnShape(i, _shapeDefinition.shapes.First(t => t.id == savedShapes[i].id), savedShapes[i].blockTypes);
            }
        }

        [Button]
        public void SpawnAllShapes(bool showImmediately = false)
        {
            // Handle spawn count logic
            _spawnRule.OnSpawnAllShapes();

            OLogger.LogNotice($"=== Shape Spawn Debug === " +
                              $"Score Level: {DataShortcut.Score.level} " +
                              $"Current Score: {DataShortcut.Score.currentScore} " +
                              $"Spawn Rule: {_spawnRule.GetDebugInfo()}\n" +
                              $"Time: {Time.time:F1}s");
            for (var i = 0; i < _shapes.Length; i++)
            {
                var shape = _shapes[i];
                if (!shape) continue;

                Core.ScenePool.Recycle(shape.gameObject);
                _shapes[i] = null;
            }

            // Use rule-based spawning instead of simple random
            for (var i = 0; i < _spawnPoints.Length; i++)
            {
                var spawnResult = _spawnRule.GenerateShapeBlockTypes(_shapeDefinition.defaultBlockCount);
                var shapeDatum = _shapeDefinition.GetRandomShape(spawnResult.isMultiColor);

                _shapes[i] = SpawnShape(i, shapeDatum, spawnResult.blockTypes);
                if (showImmediately)
                {
                    _shapes[i].Show();
                }

                // Log spawn info for debugging
                OLogger.Log($"Spawned shape {i}: {spawnResult.debugInfo}");
            }
        }

        private Shape SpawnShape(int index, ShapeDefinition.Datum datum, List<BlockType> blockTypes)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject, _shapeContainer).GetComponent<Shape>();
            shape.OTransform.localPosition = _spawnPoints[index].localPosition;
            shape.Init(datum, blockTypes);
            return shape;
        }

        public void ShowAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.Show();
            }
        }

        public Shape GetShape(int index)
        {
            return _shapes[index];
        }

        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }

        public void Despawn(Shape shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }

        public List<BoardData.Shape> GetSavedData()
        {
            return _shapes.Select(t => t == null ? null : new BoardData.Shape() { id = t.Id, blockTypes = t.BlockTypes }).ToList();
        }

        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                Despawn(shape);
            }

            // Reset the spawn rule system
            _spawnRule.Reset();
        }

        private void HandleScoreLevelUp(OnScoreLevelUpEvent e)
        {
            // Update spawn rules when level changes
            _spawnRule.SetLevel(e.level);
            OLogger.Log($"ShapeSpawner: Updated spawn rules to level {e.level}");
        }
        
        private void HandleMapAnalysisFinished(OnMapAnalysisJobFinishedEvent obj)
        {
            _spawnRule.UpdateMapAnalysis(obj.analysisResults);
        }

        private void OnDestroy()
        {
            this.EventUnsubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
            this.EventUnsubscribe<OnMapAnalysisJobFinishedEvent>(HandleMapAnalysisFinished);
        }

        #if UNITY_EDITOR
        [Button("Run Shape Spawn Tests")]
        private void RunTests()
        {
            // OP.BlockSand.Tests.ShapeSpawnRuleTests.RunBasicTests();
        }

        [Button("Show Spawn Debug Info")]
        private void ShowDebugInfo()
        {
            var debugInfo = _spawnRule.GetDebugInfo();
            Debug.Log($"Shape Spawn Debug: {debugInfo}");
        }
        #endif
    }
}