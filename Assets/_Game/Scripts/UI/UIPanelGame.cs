using System;
using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OP.BlockSand;
using PrimeTween;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [ReferenceInBackground]
    public class UIPanelGame : UIBasePanel, IContainRewardTarget
    {
        [SerializeField, ReferenceValue]
        private Animator _animator;

        [SerializeField, ReferenceValue("ButtonPause")]
        private Button _btnPause;

        [SerializeField, ReferenceValue("PanelBottom/ButtonBomb")]
        private Button _boosterBomb;

        [SerializeField, ReferenceValue("PanelBottom/ButtonAds")]
        private Button _buttonAds;

        [SerializeField, ReferenceValue("PanelBottom/ButtonShop")]
        private Button _buttonShop;

        [SerializeField, ReferenceValue("PanelTop/PanelGem")]
        private UICurrency _panelGem;

        [Header("Score Progress")]
        [SerializeField, ReferenceValue("ScoreProgressBar/LabelCurrentScore")]
        private TMP_Text _labelCurrentScore;

        [SerializeField, ReferenceValue("ScoreProgressBar/Progress")]
        private Image _scoreProgressImage;

        [SerializeField, ReferenceValue("ScoreProgressBar/LevelGroupCurrent/LabelScore")]
        private TMP_Text _labelCurrentGroupScore;

        [SerializeField, ReferenceValue("ScoreProgressBar/LevelGroupTarget/LabelScore")]
        private TMP_Text _labelTargetGroupScore;
        
        [SerializeField, ReferenceValue("PanelTop/PanelHighScore/LabelScore")]
        private TMP_Text _labelHighScore;

        [Header("Game Bounds")]
        [SerializeField]
        private RectTransform _gameBounds;

        private readonly int _showAnimation = Animator.StringToHash("PanelGame_Show");

        private long _groupScoreStart;
        private long _groupScoreTarget;
        private long _currentScore;
        
        private Tween _scoreTween;

        public override void Init(string id)
        {
            base.Init(id);

            this.EventSubscribe<GameStateChangedEvent>(HandleOnGameStateChanged);
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayReverse(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            Setup();
            
            this.EventSubscribe<OnScoreChangedEvent>(HandleOnScoreChanged);
        }

        protected override void OnAfterFocus()
        {
            _btnPause.onClick.AddListener(PauseOnClick);

            _boosterBomb.onClick.AddListener(BombOnClicked);
            _buttonAds.onClick.AddListener(AdsOnClicked);
            _buttonShop.onClick.AddListener(ShopOnClicked);
        }
        
        protected override void OnBeforeLostFocus()
        {
            _btnPause.onClick.RemoveListener(PauseOnClick);

            _boosterBomb.onClick.RemoveListener(BombOnClicked);
            _buttonAds.onClick.RemoveListener(AdsOnClicked);
            _buttonShop.onClick.RemoveListener(ShopOnClicked);
            
            this.EventUnsubscribe<OnScoreChangedEvent>(HandleOnScoreChanged);
        }

        private void Setup()
        {
            SetupScore();
            _panelGem.Init();
        }

        private void SetupScore()
        {
            var scoreData = DataShortcut.Score;
            _currentScore = scoreData.currentScore;
            _labelCurrentScore.text = $"{_currentScore.ToFormattedString(3, true, 7)}";
            (_groupScoreStart, _groupScoreTarget) = scoreData.config.GetScoreRangeForLevel(scoreData.level);
            _labelCurrentGroupScore.text = $"{_groupScoreStart.ToFormattedString(3, false)}";
            _labelTargetGroupScore.text = $"{_groupScoreTarget.ToFormattedString(3, false)}";
            _scoreProgressImage.fillAmount = (float)(_currentScore - _groupScoreStart) / (_groupScoreTarget - _groupScoreStart);
            
            _labelHighScore.text = $"{scoreData.highscore.ToFormattedString()}";
        }

        public RectTransform GetGameBounds()
        {
            return _gameBounds;
        }
        
        private void HandleOnScoreChanged(OnScoreChangedEvent e)
        {
            if (e.currentScore <= e.lastScore)
            {
                SetupScore();
                return;
            }

            _scoreTween.Stop();

            var scoreData = DataShortcut.Score;
            (_groupScoreStart, _groupScoreTarget) = scoreData.config.GetScoreRangeForLevel(scoreData.level);
            long oldGroupScoreStart = 0;
            long oldGroupScoreTarget = 0;
            if (e.isLevelUp)
            {
                (oldGroupScoreStart, oldGroupScoreTarget) = scoreData.config.GetScoreRangeForLevel(scoreData.level - 1);
            }

            _scoreTween = Tween.Custom(_currentScore, e.currentScore, 1f, value =>
            {
                _currentScore = (long)value;
                _labelCurrentScore.text = $"{_currentScore.ToFormattedString(3, true, 7)}";
                if (e.isLevelUp)
                {
                    if (_currentScore > oldGroupScoreTarget)
                    {
                        _labelCurrentGroupScore.text = $"{_groupScoreStart.ToFormattedString(3, false)}";
                        _labelTargetGroupScore.text = $"{_groupScoreTarget.ToFormattedString(3, false)}";
                        
                        _scoreProgressImage.fillAmount = (float)(_currentScore - _groupScoreStart) / (_groupScoreTarget - _groupScoreStart);
                    }
                    else
                    {
                        _labelCurrentGroupScore.text = $"{oldGroupScoreStart.ToFormattedString(3, false)}";
                        _labelTargetGroupScore.text = $"{oldGroupScoreTarget.ToFormattedString(3, false)}";
                        
                        _scoreProgressImage.fillAmount = (float)(_currentScore - oldGroupScoreStart) / (oldGroupScoreTarget - oldGroupScoreStart);
                    }
                }
                else
                {
                    _scoreProgressImage.fillAmount = (float)(_currentScore - _groupScoreStart) / (_groupScoreTarget - _groupScoreStart);   
                }
                
                if (e.isHighScore)
                {
                    _labelHighScore.text = $"{_currentScore.ToFormattedString()}";
                }
            }).OnComplete(() =>
            {
                if (e.isLevelUp)
                {
                    UIShortcut.ShowPopup(UIKeys.Panel.POPUP_LEVEL_UP);
                }  
            });
        }

        private void PauseOnClick() => UIShortcut.ShowPopup(UIKeys.Panel.PAUSE);

        private void BombOnClicked()
        {
        }

        private void AdsOnClicked()
        {
            BlockIndicator.Toast($"Coming soon!");
        }

        private void ShopOnClicked()
        {
            UIShortcut.ShowPanel(UIKeys.Panel.SHOP);
        }

        private void HandleOnGameStateChanged(GameStateChangedEvent e)
        {
            if (e.currentState != GameState.PLAYING) return;
            Setup();
        }

        public IRewardTarget GetRewardTarget<T>(T type) where T : Enum
        {
            if (typeof(T) == typeof(CurrencyType))
            {
                var currencyType = (CurrencyType)(object)type;
                return currencyType == CurrencyType.COIN ? _panelGem : null;
            }

            return null;
        }
    }
}